import React from "react";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { useTheme } from "@/hooks/useTheme";
import {
  HeaderContainer,
  Title,
  LeftContainer,
  RightContainer,
  BackButton,
  ActionButton,
  CartBadge,
  CartBadgeText,
} from "./styles";

interface HeaderProps {
  title: string;
  showCart?: boolean;
  showBack?: boolean;
  onBackPress?: () => void;
}

const Header = ({
  title,
  showCart = true,
  showBack = false,
  onBackPress,
}: HeaderProps) => {
  const router = useRouter();
  const { theme } = useTheme();
  const { cartList } = useSelector((state: RootState) => state.orders);

  return (
    <HeaderContainer>
      {showBack && (
        <LeftContainer>
          <BackButton onPress={onBackPress || (() => router.back())}>
            <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
          </BackButton>
        </LeftContainer>
      )}
      <Title>{title}</Title>
      <RightContainer>
        {showCart && (
          <ActionButton onPress={() => router.push("/(protected)/cart")}>
            <Ionicons name="cart-outline" size={24} color={theme.colors.text} />
            {cartList?.cartItems?.length > 0 && (
              <CartBadge>
                <CartBadgeText>{cartList?.cartItems?.length}</CartBadgeText>
              </CartBadge>
            )}
          </ActionButton>
        )}
      </RightContainer>
    </HeaderContainer>
  );
};

export default Header;

import { ErrorToast, BaseToast } from "react-native-toast-message";
import { styled } from "@/utils/styled";

export const StyledErrorToast = styled(ErrorToast).attrs<{ theme: any }>(
  ({ theme }) => ({
    style: {
      borderLeftColor: theme.colors.error,
      backgroundColor: theme.colors.card,
    },
    containerStyle: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
    },
    contentContainerStyle: {
      paddingHorizontal: 15,
      backgroundColor: theme.colors.card,

    },
    text1Style: {
      fontSize: 15,
      fontWeight: "500",
      color: theme.colors.text,
    },
    text2Style: {
      fontSize: 13,
      color: theme.colors.gray,
    },  
  })
)``;

export const StyledSuccessToast = styled(BaseToast).attrs<{ theme: any }>(
  ({ theme }) => ({
    style: {
      ...baseToastStyle,
      borderLeftColor: theme.colors.success,
      backgroundColor: theme.colors.card,
    },
    containerStyle: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
    },
    contentContainerStyle: {
      paddingHorizontal: 15,
      backgroundColor: theme.colors.card,

    },
    text1Style: {
      ...baseTextStyle,
      color: theme.colors.text,
    },
    text2Style: {
      fontSize: 13,
      color: theme.colors.gray,
    },
  })
)``;

const baseToastStyle = {
  backgroundColor: "transparent",
  elevation: 0,
  shadowOpacity: 0,
  borderRadius: 12,
  padding: 0,
  margin: 0,
};

const baseTextStyle = {
  fontSize: 15,
  fontWeight: "500",
};

import React from "react";
import { useTranslation } from "react-i18next";
import { OrderStatusEnum, OrderType } from "@/types/order";
import { StatusIndicator, InfoRow } from "@/components/atoms";
import {
  Container,
  HeaderContainer,
  OrderNumberContainer,
  OrderNumber,
  OrderDate,
  StatusContainer,
  InfoGrid,
} from "./styles";
import { formatDate } from "@/utils/common";

interface OrderInfoCardProps {
  orderNumber: string;
  orderDate: string;
  status: OrderStatusEnum;
  statusLabel: string;
  totalAmount: string;
  paymentStatus?: string;
  orderType?: string;
}

const OrderInfoCard: React.FC<OrderInfoCardProps> = ({
  orderNumber,
  orderDate,
  status,
  statusLabel,
  totalAmount,
  paymentStatus,
  orderType,
}) => {
  const { t } = useTranslation();

  return (
    <Container>
      <HeaderContainer>
        <OrderNumberContainer>
          <OrderNumber>#{orderNumber}</OrderNumber>
          <OrderDate>{orderDate}</OrderDate>
        </OrderNumberContainer>
        <StatusContainer>
          <StatusIndicator status={status} label={statusLabel} size="medium" />
        </StatusContainer>
      </HeaderContainer>

      <InfoGrid>
        <InfoRow
          label={t("order.total")}
          value={`₹${totalAmount}`}
          variant="bold"
        />
        {paymentStatus && (
          <InfoRow
            label={t("order.payment_status")}
            value={paymentStatus === "1" ? t("order.paid") : t("order.unpaid")}
            variant={paymentStatus === "1" ? "accent" : "default"}
          />
        )}
        {orderType && (
          <InfoRow
            label={t("order.type")}
            value={
              Number(orderType) === OrderType.Direct
                ? t("order.direct")
                : t("order.complient")
            }
          />
        )}
      </InfoGrid>
    </Container>
  );
};

export default OrderInfoCard;

import React, { useEffect, useState } from "react";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import { getComplaintsAction } from "@/store/actions/complaint";
import {
  Container,
  Content,
  Card,
  Title,
  Label,
  Value,
  StatusBadge,
  StatusText,
  ErrorText,
  EmptyContainer,
  EmptyText,
  FloatingActionButton,
  ImageContainer,
  ComplaintImage,
  Header,
} from "@/styles/Complaint.styles";
import { Ionicons } from "@expo/vector-icons";
import { LoadingOverlay, SearchBar } from "@/components";
import { useTheme } from "@/hooks/useTheme";
import { View } from "react-native";

const ComplaintsScreen = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { complaints } = useAppSelector((state: RootState) => state.complaints);
  const { theme } = useTheme();
  const [search, setSearch] = useState("");
  useEffect(() => {
    const fetchComplaints = async () => {
      try {
        setIsLoading(true);
        setError(null);
        await dispatch(getComplaintsAction({})).unwrap();
      } catch (err) {
        setError(t("error_loading_complaints"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchComplaints();
  }, [dispatch, t]);

  if (isLoading) {
    return <LoadingOverlay isLoading={isLoading} />;
  }

  if (error) {
    return (
      <EmptyContainer>
        <ErrorText>{error}</ErrorText>
      </EmptyContainer>
    );
  }

  if (!complaints.length) {
    return (
      <EmptyContainer>
        <EmptyText>{t("no_complaints")}</EmptyText>
      </EmptyContainer>
    );
  }

  const handleStatusPress = () => {
    console.log("handleStatusPress");
  };

  return (
    <Container>
      <Header>
        <SearchBar
          onSearch={setSearch}
          onFilterPress={handleStatusPress}
          value={search}
        />
      </Header>
      <Content>
        {complaints.map((complaint) => (
          <Card
            key={complaint.id}
            onPress={() => router.push(`/complaint/${complaint.id}`)}
          >
            <Title>
              {t("complaint.title")} #{complaint.id}
            </Title>
            <Label>{t("complaint.status")}</Label>
            <StatusBadge status={complaint.status}>
              <StatusText status={complaint.status}>
                {t(`status.${complaint.status}`)}
              </StatusText>
            </StatusBadge>

            {complaint.image && (
              <ImageContainer>
                <ComplaintImage
                  source={{ uri: complaint.image }}
                  contentFit="contain"
                />
              </ImageContainer>
            )}

            <Label>{t("complaint.description")}</Label>
            <Value>{complaint.description}</Value>

            <Label>{t("complaint.serial_number")}</Label>
            <Value>{complaint.serial_number}</Value>

            <Label>{t("complaint.created_at")}</Label>
            <Value>{new Date(complaint.created_at).toLocaleDateString()}</Value>
          </Card>
        ))}
      </Content>
      <FloatingActionButton
        onPress={() => {
          router.push("/complaint/new");
        }}
      >
        <Ionicons name="add" size={24} color={theme.colors.white} />
      </FloatingActionButton>
    </Container>
  );
};

export default ComplaintsScreen;

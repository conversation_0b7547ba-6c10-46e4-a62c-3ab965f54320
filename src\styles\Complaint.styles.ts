import {
  View,
  Text,
  Pressable,
  TextInput,
  ScrollView,
  Modal,
  Dimensions,
} from "react-native";
import { Image } from "expo-image";
import { styled } from "../utils/styled";
export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const Content = styled(ScrollView)`
  flex: 1;
  padding: 16px;
`;

export const Header = styled.View`
  padding: 16px;
`;

export const Card = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.cardbackground};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  elevation: 2;
  shadow-color: ${({ theme }) => theme.colors.black};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
`;

export const Title = styled(Text)`
  font-size: 20px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
`;

export const Label = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 8px;
`;

export const Value = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
`;

export const StatusBadge = styled(View)<{ status: string }>`
  padding: 6px 12px;
  border-radius: 16px;
  background-color: ${(props) => {
    switch (props.status) {
      case "pending":
        return props.theme.colors.statusPendingBg;
      case "in_review":
        return props.theme.colors.statusInReviewBg;
      case "estimation_sent":
        return props.theme.colors.statusEstimationBg;
      case "approved":
        return props.theme.colors.statusApprovedBg;
      case "repair":
        return props.theme.colors.statusRepairBg;
      case "in_progress":
        return props.theme.colors.statusInProgressBg;
      case "resolved":
        return props.theme.colors.statusResolvedBg;
      case "rejected":
        return props.theme.colors.statusRejectedBg;
      case "closed":
        return props.theme.colors.statusClosedBg;
      default:
        return props.theme.colors.lightGray;
    }
  }};
`;

export const StatusText = styled(Text)<{ status: string }>`
  font-size: 14px;
  color: ${(props) => {
    switch (props.status) {
      case "pending":
        return props.theme.colors.statusPending;
      case "in_review":
        return props.theme.colors.statusInReview;
      case "estimation_sent":
        return props.theme.colors.statusEstimation;
      case "approved":
        return props.theme.colors.statusApproved;
      case "repair":
        return props.theme.colors.statusRepair;
      case "in_progress":
        return props.theme.colors.statusInProgress;
      case "resolved":
        return props.theme.colors.statusResolved;
      case "rejected":
        return props.theme.colors.statusRejected;
      case "closed":
        return props.theme.colors.statusClosed;
      default:
        return props.theme.colors.gray;
    }
  }};
`;

export const Input = styled(TextInput)`
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 16px;
`;

export const SubmitButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.primary};
  border-radius: 8px;
  padding: 16px;
  align-items: center;
  margin-top: 16px;
`;

export const SubmitButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
`;

export const ErrorText = styled(Text)`
  color: ${({ theme }) => theme.colors.error};
  font-size: 14px;
  margin-top: 4px;
  margin-bottom: 16px;
`;

export const LoadingContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

export const LoadingText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  margin-top: 16px;
`;

export const EmptyContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 32px;
`;

export const EmptyText = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
  text-align: center;
  margin-top: 16px;
`;

export const ActionButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.primary};
  border-color: ${({ theme }) => theme.colors.primary};
  border-width: 1px;
  border-radius: 8px;
  padding: 12px;
  align-items: center;
  margin-top: 8px;
`;

export const ActionButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 14px;
  font-weight: 600;
`;

export const FloatingActionButton = styled(Pressable)`
  position: absolute;
  right: 16px;
  bottom: 16px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background-color: ${({ theme }) => theme.colors.primary};
  justify-content: center;
  align-items: center;
  elevation: 4;
  shadow-color: ${({ theme }) => theme.colors.black};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.25;
  shadow-radius: 4px;
`;

// New Complaint Form Styles
export const FormContainer = styled(ScrollView)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const FormContent = styled(View)`
  padding: 16px;
`;

export const FormInputGroup = styled(View)`
  margin-bottom: 20px;
`;

export const FormLabel = styled(Text)`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 8px;
`;

export const FormInput = styled(TextInput)`
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  color: ${({ theme }) => theme.colors.text};
`;

export const FormTextArea = styled(FormInput)`
  height: 120px;
  padding-top: 12px;
`;

export const ImageUploadButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  border-style: dashed;
  height: 200px;
  justify-content: center;
  align-items: center;
  overflow: hidden;
`;

export const UploadPlaceholder = styled(View)`
  align-items: center;
`;

export const UploadText = styled(Text)`
  margin-top: 8px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.gray};
`;

export const UploadedImage = styled(Image)`
  width: 100%;
  height: 100%;
  content-fit: cover;
`;

export const FormSubmitButton = styled(Pressable)`
  background-color: ${({ theme }) => theme.colors.primary};
  padding: 16px;
  border-radius: 8px;
  align-items: center;
  margin-top: 20px;
`;

export const FormSubmitButtonText = styled(Text)`
  color: ${({ theme }) => theme.colors.white};
  font-size: 16px;
  font-weight: 600;
`;

export const ImageContainer = styled(Pressable)`
  margin: 10px 0;
`;

export const ImageScroll = styled.ScrollView`
  flex-direction: row;
`;

export const ComplaintImage = styled(Image)`
  width: 100px;
  height: 100px;
  border-radius: 8px;
  margin-right: 8px;
`;

export const FullScreenModal = styled(Modal)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.black};
`;

export const FullScreenImageContainer = styled(View)`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.white};
`;

export const FullScreenImage = styled(Image)`
  width: ${Dimensions.get("window").width}px;
  height: ${Dimensions.get("window").height}px;
`;

export const CloseButton = styled(Pressable)`
  position: absolute;
  top: 40px;
  right: 20px;
  z-index: 1;
  padding: 8px;
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 50px;
`;

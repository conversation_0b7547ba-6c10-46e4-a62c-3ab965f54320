import React, { useCallback } from "react";
import { FlatList, ListRenderItem } from "react-native";
import { VendorDetails } from "@/types/vendor";
import { VendorCard } from "@/components/molecules";

interface VendorListProps {
  vendors: VendorDetails[];
  onEdit: (vendor: VendorDetails) => void;
  onDelete: (vendor: VendorDetails) => void;
  refreshing?: boolean;
  onRefresh?: () => void;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  loading?: boolean;
}

const VendorList: React.FC<VendorListProps> = ({
  vendors,
  onEdit,
  onDelete,
  refreshing = false,
  onRefresh,
  onEndReached,
  onEndReachedThreshold = 0.1,
  loading = false,
}) => {
  const renderVendorItem: ListRenderItem<VendorDetails> = useCallback(
    ({ item }) => (
      <VendorCard
        vendor={item}
        onEdit={() => onEdit(item)}
        onDelete={() => onDelete(item)}
      />
    ),
    [onEdit, onDelete]
  );

  const keyExtractor = useCallback(
    (item: VendorDetails) => item.vendor_id?.toString() || "",
    []
  );

  const handleEndReached = useCallback(() => {
    if (!loading && onEndReached) {
      onEndReached();
    }
  }, [loading, onEndReached]);

  return (
    <FlatList
      data={vendors}
      renderItem={renderVendorItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      refreshing={refreshing}
      onRefresh={onRefresh}
      onEndReached={handleEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      contentContainerStyle={{
        paddingVertical: 24,
      }}
    />
  );
};

export default VendorList;

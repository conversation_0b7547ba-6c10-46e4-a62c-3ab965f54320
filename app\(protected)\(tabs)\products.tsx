import React, { useState, useEffect } from "react";
import { KeyboardAvoidingView } from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { RootState, useAppDispatch, useAppSelector } from "@/store/store";
import { useTheme } from "@/hooks/useTheme";
import {
  Container,
  Header,
  ProductList,
  ProductCard,
  ProductImage,
  ProductInfo,
  ProductName,
  ProductPrice,
  EmptyContainer,
  EmptyText,
  SortContainer,
  SortButton,
  SortText,
  SortValue,
  ItemCount,
  ActiveFiltersContainer,
  ActiveFilterChip,
  ActiveFilterText,
  FilterChipsRow,
} from "@/styles/ProductList.styles";
import { ProductType } from "@/types/product";
import { getProductCategoryListAction } from "@/store/actions/product";
import { getProductListAction } from "@/store/actions/product";
import { LoadingOverlay, SearchBar } from "@/components";
import { FilterBottomSheet, SortBottomSheet } from "@/components/organisms";
import Toast from "react-native-toast-message";
import { useDebounce } from "@/utils/useDebounce";
import { Image } from "expo-image";
import { sortOptions } from "@/utils/common";
import { SortOptionList } from "@/components/organisms/SortBottomSheet";

const numColumns = 2;

const ProductListScreen = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { theme } = useTheme();
  const dispatch = useAppDispatch();

  const { filters, categoryList, products, current_page, last_page } =
    useAppSelector((state: RootState) => state.product);
  const [selectedSort, setSelectedSort] = useState<SortOptionList | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [search, setSearch] = useState<string>("");
  const [sortBottomSheetVisible, setSortBottomSheetVisible] = useState(false);
  const [filterBottomSheetVisible, setFilterBottomSheetVisible] =
    useState(false);
  const debouceSearch = useDebounce(search, 300);

  const sortedProducts = products;

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        if (!categoryList || categoryList.length === 0) {
          await dispatch(getProductCategoryListAction({})).unwrap();
        }

        if (products.length === 0) {
          await dispatch(getProductListAction({ page: 1 })).unwrap();
        }
      } catch (error) {
        console.error("Error fetching products:", error);
        Toast.show({
          type: "error",
          text1: error?.message,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleProductPress = (slug: string) => {
    router.push(`/product/${slug}`);
  };

  const handleSearch = async (text: string) => {
    await dispatch(getProductListAction({ ...filters, search: text }));
  };

  useEffect(() => {
    if (debouceSearch && debouceSearch.length > 3) {
      handleSearch(debouceSearch);
    }
  }, [debouceSearch]);

  const handleMoreData = () => {
    try {
      setIsLoadingMore(true);
      const nextPage = current_page + 1;
      if (current_page < last_page && !isLoadingMore) {
        dispatch(getProductListAction({ ...filters, page: nextPage }));
      }
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to load more products.",
      });
    } finally {
      setIsLoadingMore(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    try {
      dispatch(getProductListAction({ ...filters, page: 1 }));
    } catch (error) {
      Toast.show({
        type: "error",
        text1: error?.message || "Failed to refresh products.",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleSortPress = () => {
    setSortBottomSheetVisible(true);
  };

  const handleSortBottomSheetDismiss = () => {
    setSortBottomSheetVisible(false);
  };

  const handleSortApply = async (sortOption: SortOptionList | null) => {
    setSortBottomSheetVisible(false);
    if (sortOption) {
      try {
        setSelectedSort(sortOption);
        setIsRefreshing(true);
        await dispatch(
          getProductListAction({
            ...filters,
            order_by: sortOption.order_by,
            sort_order: sortOption.sort_order,
            page: 1,
          })
        );
      } catch (error) {
        Toast.show({
          type: "error",
          text1: error?.message || "Failed to apply sort",
        });
      } finally {
        setIsRefreshing(false);
      }
    }
  };

  const handleSortClear = async () => {
    setSortBottomSheetVisible(false);
    setSelectedSort(null);
    await dispatch(getProductListAction({}));
  };

  const handleFilterPress = () => {
    setFilterBottomSheetVisible(true);
  };

  const handleFilterBottomSheetDismiss = () => {
    setFilterBottomSheetVisible(false);
  };

  const handleFilterApply = async (appliedCategory: number) => {
    setFilterBottomSheetVisible(false);
    if (appliedCategory) {
      try {
        setIsRefreshing(true);
        await dispatch(
          getProductListAction({
            ...filters,
            category_id: appliedCategory,
            page: 1,
          })
        );
      } catch (error) {
        Toast.show({
          type: "error",
          text1: error?.message || "Failed to apply filter",
        });
      } finally {
        setIsRefreshing(false);
      }
    }
  };

  const getAppliedCategoryNames = () => {
    if (!filters?.category_id) return [];
    return [filters.category_id].map((categoryId) => {
      const category = categoryList.find((cat) => cat.id === categoryId);
      return {
        id: categoryId,
        name: category?.title || `Category ${categoryId}`,
      };
    });
  };

  const handleRemoveFilter = async () => {
    await dispatch(getProductListAction({}));
  };
  const handleFilterClear = async () => {
    setFilterBottomSheetVisible(false);
    await dispatch(getProductListAction({}));
  };

  const renderActiveFilters = () => {
    const activeFilters = [];
    const appliedCategoryNames = getAppliedCategoryNames();
    appliedCategoryNames.forEach((category) => {
      activeFilters.push({
        label: category.name,
        categoryId: category.id,
        onRemove: () => handleRemoveFilter(),
      });
    });

    if (activeFilters.length === 0) return null;

    return (
      <ActiveFiltersContainer>
        <FilterChipsRow>
          {activeFilters.map((filter, index) => (
            <ActiveFilterChip
              key={`${filter.type}-${filter.categoryId || index}`}
              onPress={filter.onRemove}
            >
              <ActiveFilterText>{filter.label}</ActiveFilterText>
              <Ionicons name="close" size={14} color={theme.colors.white} />
            </ActiveFilterChip>
          ))}
        </FilterChipsRow>
      </ActiveFiltersContainer>
    );
  };

  const renderProduct = ({ item }: { item: ProductType }) => {
    return (
      <ProductCard isList={false} onPress={() => handleProductPress(item.slug)}>
        <ProductImage>
          <Image
            source={{ uri: item?.main_image?.url }}
            style={{ width: "100%", height: "100%" }}
            contentFit="cover"
          />
        </ProductImage>
        <ProductInfo>
          <ProductName>{item?.title}</ProductName>
          <ProductPrice>₹{Math.round(parseFloat(item?.price))}</ProductPrice>
          {/* <ProductDescription numberOfLines={2}>
            {item?.sort_description}
          </ProductDescription> */}
        </ProductInfo>
      </ProductCard>
    );
  };

  if (isLoading && !isRefreshing && !isLoadingMore && current_page === 1) {
    return <LoadingOverlay isLoading={isLoading} />;
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      contentContainerStyle={{ flexGrow: 1 }}
    >
      <Container>
        <Header>
          <SearchBar
            onSearch={setSearch}
            onFilterPress={handleFilterPress}
            value={search}
          />
        </Header>

        <SortContainer>
          <ItemCount>{sortedProducts.length} items</ItemCount>
          <SortButton
            hitSlop={20}
            onPress={handleSortPress}
            style={{ marginLeft: "auto" }}
          >
            <SortText>{t("sort")}: </SortText>
            <SortValue>{selectedSort?.label || "Default"}</SortValue>
            <Ionicons
              name="chevron-down"
              size={16}
              color={theme.colors.primary}
            />
          </SortButton>
        </SortContainer>

        {renderActiveFilters()}

        <ProductList
          scrollEnabled
          data={sortedProducts}
          renderItem={renderProduct}
          keyExtractor={(item: ProductType) => String(item.product_id)}
          numColumns={numColumns}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 16 }}
          ListEmptyComponent={
            <EmptyContainer>
              <EmptyText>{t("common.no_products_found")}</EmptyText>
            </EmptyContainer>
          }
          onRefresh={handleRefresh}
          refreshing={isRefreshing}
          onEndReached={handleMoreData}
          onEndReachedThreshold={0.5}
          ListFooterComponent={
            isLoadingMore ? <LoadingOverlay isLoading /> : null
          }
        />

        <SortBottomSheet
          isVisible={sortBottomSheetVisible}
          onClose={handleSortBottomSheetDismiss}
          onDismiss={handleSortBottomSheetDismiss}
          onApply={handleSortApply}
          onClear={handleSortClear}
          sortOptions={sortOptions}
        />

        <FilterBottomSheet
          isVisible={filterBottomSheetVisible}
          onDismiss={handleFilterBottomSheetDismiss}
          onClose={handleFilterBottomSheetDismiss}
          onApply={handleFilterApply}
          onClear={handleFilterClear}
          categoryList={categoryList}
        />
      </Container>
    </KeyboardAvoidingView>
  );
};

export default ProductListScreen;

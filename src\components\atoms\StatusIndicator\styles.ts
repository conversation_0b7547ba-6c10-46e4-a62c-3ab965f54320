import { styled } from "@/utils/styled";
import { View, Text } from "react-native";
import { OrderStatusEnum } from "@/types/order";

interface StatusIndicatorProps {
  status: OrderStatusEnum;
  size?: "small" | "medium" | "large";
}

export const Container = styled(View)<StatusIndicatorProps>`
  padding: ${({ size }) => {
    switch (size) {
      case "small":
        return "4px 8px";
      case "large":
        return "8px 16px";
      default:
        return "6px 12px";
    }
  }};
  border-radius: ${({ size }) => {
    switch (size) {
      case "small":
        return "12px";
      case "large":
        return "20px";
      default:
        return "16px";
    }
  }};
  background-color: ${({ theme, status }) => {
    switch (status) {
      case OrderStatusEnum.Pending:
        return theme.colors.statusPendingBg || "#FFF3CD";
      case OrderStatusEnum.Approved:
        return theme.colors.statusInProgressBg || "#D1ECF1";
      case OrderStatusEnum.InDispatch:
        return theme.colors.statusInProgressBg || "#D1ECF1";
      case OrderStatusEnum.Dispatched:
        return theme.colors.statusResolvedBg || "#D4EDDA";
      case OrderStatusEnum.Received:
        return theme.colors.statusResolvedBg || "#D4EDDA";
      case OrderStatusEnum.Cancelled:
        return theme.colors.statusRejectedBg || "#F8D7DA";
      default:
        return theme.colors.lightGray || "#F8F9FA";
    }
  }};
  align-self: flex-start;
`;

export const StatusText = styled(Text)<StatusIndicatorProps>`
  font-size: ${({ size }) => {
    switch (size) {
      case "small":
        return "12px";
      case "large":
        return "16px";
      default:
        return "14px";
    }
  }};
  font-weight: 600;
  color: ${({ theme, status }) => {
    switch (status) {
      case OrderStatusEnum.Pending:
        return theme.colors.statusPending || "#856404";
      case OrderStatusEnum.Approved:
        return theme.colors.statusInProgress || "#0C5460";
      case OrderStatusEnum.InDispatch:
        return theme.colors.statusInProgress || "#0C5460";
      case OrderStatusEnum.Dispatched:
        return theme.colors.statusResolved || "#155724";
      case OrderStatusEnum.Received:
        return theme.colors.statusResolved || "#155724";
      case OrderStatusEnum.Cancelled:
        return theme.colors.statusRejected || "#721C24";
      default:
        return theme.colors.gray || "#6C757D";
    }
  }};
`;

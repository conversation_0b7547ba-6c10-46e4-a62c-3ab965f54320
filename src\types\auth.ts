import { ApiResponse, UserType } from "./api";

export interface DefaultDetails {
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

export interface LoginOTPPayload {
  country_code_alpha: string;
  mobile: string;
}

export interface RegisterOTPPayload {
  first_name: string;
  last_name: string;
  country_code_alpha: string;
  mobile: string;
  created_by_id?: number;
}

export interface LoginOTPVerifyPayload extends LoginOTPPayload {
  otp: string;
}

export interface UserDetails extends DefaultDetails {
  id: number;
  first_name: string;
  last_name: string;
  name: string;
  email: string;
  email_verified_at: boolean;
  is_profile_complete: number;
  country_code_alpha: string;
  mobile: string;
  mobile_otp: number;
  role_id: UserType;
  address_line_one: string;
  address_line_two: string;
  city_id: number;
  state_id: number;
  country_id: number;
  post_code: string;
  gst_number: string;
  address?: any[];
}

export interface LoginOTPVerifyResponse extends ApiResponse {
  is_sale_person?: boolean;
  token: string;
  user_details: UserDetails;
}
export type LoginOTPVerifyThunkPayload = {
  payload: LoginOTPVerifyPayload;
  isSalePerson?: boolean;
};
export interface CountryCode {
  id: number;
  full_name: string;
  dialling_code: string;
  country_code_alpha: string;
  flag: string;
  postcode_regexp?: string | null;
}

export interface StateList {
  id: number;
  name: string;
  country_id: number;
}

export interface CityList extends StateList {}

export interface CountryCodeListResponse extends ApiResponse {
  data: CountryCode[];
}

export interface StateListResponse extends ApiResponse {
  data: StateList[];
}

export interface CityListResponse extends ApiResponse {
  data: CityList[];
}

export interface UpdateUserPayload extends UserDetails {}

export interface SettingsList {
  payment_details: {
    id: number;
    identified_key: string;
    identifier_value: string;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    qr_code: string | null;
  }[];
  payment_type: Record<string, string>;
  fix_settings: {
    supported_file_format: {
      general: string;
      image: string;
    };
    file_size: {
      general: number;
      image: number;
      total_file_size: number;
    };
    supported_file_extension: {
      general: string;
    };
  };
}

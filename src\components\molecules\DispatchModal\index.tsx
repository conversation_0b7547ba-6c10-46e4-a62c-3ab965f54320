import React from "react";
import {
  Modal,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { useTranslation } from "react-i18next";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { DispatchData } from "../OrderTimeline";
import {
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalTitle,
  CloseButton,
} from "./styles";
import FormTemplate from "@/template/FormTemplate";
import { DispatchForm } from "@/components/organisms/DispatchModalForm";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";

const dispatchSchema = yup.object().shape({
  vehical_no: yup.string().required("Vehicle number is required"),
  tracking: yup.string().required("Tracking ID is required"),
  tracking_url: yup.string().url("Please enter a valid URL"),
  notes: yup.string(),
  courier_name: yup.string().required("Courier name is required"),
});

interface DispatchModalProps {
  visible: boolean;
  onCancel: () => void;
  onConfirm: (data: DispatchData) => Promise<void>;
  loading?: boolean;
}

export const DispatchModal: React.FC<DispatchModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  loading = false,
}) => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  const handleFormSubmit = async (data: DispatchData) => {
    try {
      await onConfirm(data);
    } catch (error) {
      console.error("Error while confirming dispatch:", error);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <ModalOverlay>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          keyboardVerticalOffset={80}
          style={{ width: "100%", maxWidth: 400 }}
        >
          <ModalContent>
            <ModalHeader>
              <ModalTitle>{t("order.dispatch_order")}</ModalTitle>
              <CloseButton onPress={onCancel} disabled={loading}>
                <Ionicons name="close" size={24} color={theme.colors.text} />
              </CloseButton>
            </ModalHeader>

            <FormTemplate<DispatchData>
              Component={(props) => (
                <>
                  <DispatchForm
                    {...props}
                    loading={loading}
                    t={t}
                    theme={theme}
                    hideButtons
                  />
                </>
              )}
              defaultValues={{
                vehical_no: "",
                tracking: "",
                tracking_url: "",
                notes: "",
                courier_name: "",
              }}
              resolver={yupResolver(dispatchSchema)}
              mode="onChange"
              onSubmit={handleFormSubmit}
              onCancel={onCancel}
            />
          </ModalContent>
        </KeyboardAvoidingView>
      </ModalOverlay>
    </Modal>
  );
};

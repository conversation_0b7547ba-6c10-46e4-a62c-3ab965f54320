import { ApiResponse } from "./api";

export interface Country {
  id: number;
  full_name: string;
}

export interface State {
  id: number;
  name: string;
}

export interface City {
  id: number;
  name: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
}

export interface VendorDetails {
  user_id?: number;
  vendor_id?: number;
  first_name: string;
  last_name: string;
  email: string;
  gst_number?: string;
  address_line_one?: string;
  address_line_two?: string | null;
  post_code?: string;
  state_id?: number;
  city_id?: number;
  mobile?: number;
  auth_dealer?: "1" | "0";
  country_id?: number;
  country_code_alpha?: string;
}

export interface PaginationLink {
  url: string | null;
  label: string;
  active: boolean;
}

export interface VendorListResponse {
  data: VendorDetails[];
  pagination: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  meta?: any;
}

export interface VendorListPayloadType {
  page?: number;
  search?: string;
  auth_dealer?: number;
  order_by?: number;
  sort_by?: number;
}

export interface VendorInsertUpdatePayloadType extends VendorDetails {}

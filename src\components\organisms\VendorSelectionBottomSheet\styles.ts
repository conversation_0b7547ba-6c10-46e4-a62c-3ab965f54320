import { FlatList } from "react-native";
import { styled } from "@/utils/styled";
import { BottomSheetView } from "@gorhom/bottom-sheet";

export const Title = styled.Text`
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: ${({ theme }) => theme.colors.primary};
`;

export const SearchContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.lightGray};
  border-radius: 8px;
  padding: 8px 12px;
  margin-bottom: 16px;
`;

export const SearchInput = styled.TextInput`
  flex: 1;
  margin-left: 8px;
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text};
`;

export const ListContainer = styled(FlatList)`
  flex: 1;
`;

export const VendorItem = styled.Pressable`
  flex-direction: row;
  align-items: center;
  padding: 16px 0px;
  border-bottom-width: 1px;
  border-bottom-color: ${({ theme }) => theme.colors.lightGray};
`;

export const VendorInfo = styled.View`
  flex: 1;
`;

export const VendorName = styled.Text`
  font-size: 16px;
  font-weight: 600;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const VendorId = styled.Text`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.gray};
  margin-bottom: 2px;
`;

export const EmptyContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
`;

export const EmptyText = styled.Text`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.textSecondary};
  text-align: center;
`;

export const StyledBottomSheetView = styled(BottomSheetView)`
  padding: 20px;
  flex: 1;
`;

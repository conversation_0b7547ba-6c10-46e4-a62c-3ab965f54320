import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const Container = styled(View)`
  background-color: ${({ theme }) => theme.colors.card || theme.colors.white};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  shadow-color: ${({ theme }) => theme.colors.shadow || "#000"};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 2;
`;

export const HeaderContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
`;

export const OrderNumberContainer = styled(View)`
  flex: 1;
`;

export const OrderNumber = styled(Text)`
  font-size: 20px;
  font-weight: 700;
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: 4px;
`;

export const OrderDate = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.textSecondary || theme.colors.gray};
`;

export const StatusContainer = styled(View)`
  align-items: flex-end;
`;

export const InfoGrid = styled(View)`
  gap: 8px;
  
`;

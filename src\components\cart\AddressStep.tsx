import React, { useCallback } from "react";
import { useRouter } from "expo-router";
import AddressSelection from "@/components/organisms/AddressSelection";
import CartFooter from "@/components/organisms/CartFooter";
import { useAddressManagement } from "@/hooks/useAddressManagement";

interface AddressStepProps {
  onBack: () => void;
  onContinue: () => void;
  selectedBillingAddressId?: number | null;
  selectedShippingAddressId?: number | null;
  useBillingAsShipping?: boolean;
  setSelectedBillingAddressId?: (id: number | null) => void;
  setSelectedShippingAddressId?: (id: number | null) => void;
  setUseBillingAsShipping?: (value: boolean) => void;
}

const AddressStep: React.FC<AddressStepProps> = ({
  onBack,
  onContinue,
  selectedBillingAddressId: externalBillingId,
  selectedShippingAddressId: externalShippingId,
  useBillingAsShipping: externalUseBillingAsShipping,
  setSelectedBillingAddressId: externalSetBillingId,
  setSelectedShippingAddressId: externalSetShippingId,
  setUseBillingAsShipping: externalSetUseBillingAsShipping,
}) => {
  const router = useRouter();
  const addressManagement = useAddressManagement();

  // Use external state if provided, otherwise fall back to hook state
  const selectedBillingAddressId =
    externalBillingId !== undefined
      ? externalBillingId
      : addressManagement.selectedBillingAddressId;
  const selectedShippingAddressId =
    externalShippingId !== undefined
      ? externalShippingId
      : addressManagement.selectedShippingAddressId;
  const useBillingAsShipping =
    externalUseBillingAsShipping !== undefined
      ? externalUseBillingAsShipping
      : addressManagement.useBillingAsShipping;

  // Use external setters if provided, otherwise fall back to hook handlers
  const handleBillingAddressSelection = externalSetBillingId
    ? (address: any) => externalSetBillingId(address.id)
    : addressManagement.handleBillingAddressSelection;

  const handleShippingAddressSelection = externalSetShippingId
    ? (address: any) => externalSetShippingId(address.id)
    : addressManagement.handleShippingAddressSelection;

  const handleToggleBillingAsShipping = externalSetUseBillingAsShipping
    ? (value: boolean) => externalSetUseBillingAsShipping(value)
    : addressManagement.handleToggleBillingAsShipping;

  const { handleEditAddress, getAddresses, isReadyForCheckout } =
    addressManagement;

  const addresses = getAddresses();
  const hasNoAddresses = addresses.all.length === 0;

  const handleContinueAction = useCallback(() => {
    if (hasNoAddresses) {
      router.push("/(protected)/add-address?returnStep=2");
    } else {
      onContinue();
    }
  }, [hasNoAddresses, router, onContinue]);

  return (
    <>
      <AddressSelection
        addresses={addresses.all}
        selectedBillingId={selectedBillingAddressId}
        selectedShippingId={selectedShippingAddressId}
        onSelectBilling={handleBillingAddressSelection}
        onSelectShipping={handleShippingAddressSelection}
        onEditAddress={handleEditAddress}
        useBillingAsShipping={useBillingAsShipping}
        onToggleBillingAsShipping={handleToggleBillingAsShipping}
      />

      <CartFooter
        onClearCart={onBack}
        onContinue={handleContinueAction}
        clearButtonTitle="Back"
        continueButtonTitle={hasNoAddresses ? "Add Address" : "Continue"}
        disabled={!isReadyForCheckout() && !hasNoAddresses}
      />
    </>
  );
};

export default AddressStep;

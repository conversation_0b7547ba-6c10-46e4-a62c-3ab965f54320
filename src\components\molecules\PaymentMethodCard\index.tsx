import React from "react";
import { View } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import {
  PaymentMethodCardContainer,
  PaymentMethodHeader,
  PaymentMethodContent,
  PaymentMethodIcon,
  PaymentMethodLabel,
  PaymentMethodDescription,
} from "./styles";
import { useTheme } from "@/hooks/useTheme";
import Checkbox from "@/components/atoms/Checkbox";

interface PaymentMethodCardProps {
  id: string;
  label: string;
  description?: string;
  icon: keyof typeof Ionicons.glyphMap;
  selected: boolean;
  onSelect: () => void;
  children?: React.ReactNode;
}

const PaymentMethodCard: React.FC<PaymentMethodCardProps> = ({
  id,
  label,
  description,
  icon,
  selected,
  onSelect,
  children,
}) => {
  const { theme } = useTheme();

  return (
    <PaymentMethodCardContainer selected={selected}>
      <PaymentMethodHeader>
        <PaymentMethodIcon>
          <Ionicons
            name={icon}
            size={24}
            color={selected ? theme.colors.primary : theme.colors.secondary}
          />
        </PaymentMethodIcon>
        <View style={{ flex: 1 }}>
          <PaymentMethodLabel selected={selected}>{label}</PaymentMethodLabel>
          {description && (
            <PaymentMethodDescription>{description}</PaymentMethodDescription>
          )}
        </View>
        <Checkbox checked={selected} onPress={onSelect} />
      </PaymentMethodHeader>

      {selected && children && (
        <PaymentMethodContent>{children}</PaymentMethodContent>
      )}
    </PaymentMethodCardContainer>
  );
};

export default PaymentMethodCard;

import { useTranslation } from "react-i18next";
import { Container, Content, WelcomeText } from "@/styles/Dashboard.styles";
import { useAppDispatch, useAppSelector } from "@/store/store";
import { UserType } from "@/types/api";
import { useEffect } from "react";
import {
  getCityListAction,
  getSettingsListAction,
  getStateListAction,
  getUserDetailsAction,
} from "@/store/actions/auth";
import {
  getCartListAction,
  getOrderStatusListAction,
} from "@/store/actions/order";
import { getAddressListAction } from "@/store/actions/address";
import { getVendorListAction } from "@/store/actions/vendor";
import { getProductListAction } from "@/store/actions/product";
export default function DashboardScreen() {
  const { t } = useTranslation();
  const { user, stateList, cityList } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const isSalesPerson = user?.role_id === UserType.SALESPERSON;
  useEffect(() => {
    dispatch(getUserDetailsAction({})).unwrap();
    dispatch(getProductListAction({})).unwrap();
    dispatch(getCartListAction({})).unwrap();
    dispatch(getAddressListAction({})).unwrap();
    dispatch(getSettingsListAction({})).unwrap();
    if (isSalesPerson) {
      dispatch(getVendorListAction({})).unwrap();
    }
    dispatch(getOrderStatusListAction({})).unwrap();
    if (user?.country_id && !stateList?.length) {
      dispatch(getStateListAction(user?.country_id)).unwrap();
    }
    if (user?.state_id && !cityList?.length) {
      dispatch(getCityListAction(user?.state_id)).unwrap();
    }
  }, []);
  return (
    <Container>
      <Content>
        <WelcomeText>{`${t("welcome")} ${
          user && user.first_name
        } 👋🏻`}</WelcomeText>
      </Content>
    </Container>
  );
}

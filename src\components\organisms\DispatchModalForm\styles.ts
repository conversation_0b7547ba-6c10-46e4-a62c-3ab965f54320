import { styled } from "@/utils/styled";
import { Pressable, Text, View } from "react-native";

export const Form = styled(View)`
  padding: 20px;
`;

export const ButtonContainer = styled(View)`
  flex-direction: row;
  justify-content: space-between;
  padding: 20px;
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.divider || "#E9ECEF"};
  gap: 12px;
  background-color: ${({ theme }) => theme.colors.card};
`;

export const CancelButton = styled(Pressable)`
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.divider || "#E9ECEF"};
  background-color: transparent;
  align-items: center;
  justify-content: center;
`;

export const ConfirmButton = styled(Pressable)`
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: ${({ theme }) => theme.colors.primary};
  align-items: center;
  justify-content: center;
`;

export const Buttontext = styled(Text)`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text};
`;


export const ButtonText = styled(Text)<{ variant?: "cancel" | "confirm" }>`
  font-size: 16px;
  font-weight: 500;
  color: ${({ theme, variant }) => 
    variant === "cancel" ? theme.colors.text : theme.colors.white};
`;
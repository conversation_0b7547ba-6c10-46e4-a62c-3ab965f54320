import React from "react";
import { Pressable, TextInputProps } from "react-native";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { useTheme } from "@/hooks/useTheme";
import { SearchBarContainer, SearchInput } from "./styles";

interface SearchBarProps extends TextInputProps {
  onSearch?: (text: string) => void;
  onFilterPress?: () => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  onFilterPress,
  ...props
}) => {
  const { theme } = useTheme();

  return (
    <SearchBarContainer>
      <Ionicons
        name="search"
        size={20}
        color={theme.colors.text}
        style={{ marginRight: 8 }}
      />
      <SearchInput
        placeholder="Search order"
        placeholderTextColor={theme.colors.text}
        onChangeText={onSearch}
        {...props}
      />
      <Pressable hitSlop={20} onPress={onFilterPress}>
        <MaterialCommunityIcons
          name="tune-vertical-variant"
          size={20}
          color={theme.colors.text}
        />
      </Pressable>
    </SearchBarContainer>
  );
};

export default SearchBar;

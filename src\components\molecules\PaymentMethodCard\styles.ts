import { styled } from "@/utils/styled";
import { View, Text } from "react-native";

export const PaymentMethodCardContainer = styled(View)<{ selected: boolean }>`
  background-color: ${({ theme, selected }) =>
    selected ? theme.colors.primaryLight : theme.colors.background};
  border: 1px solid
    ${({ theme, selected }) =>
      selected ? theme.colors.primary : theme.colors.border};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
`;

export const PaymentMethodHeader = styled(View)`
  flex-direction: row;
  align-items: center;
`;

export const PaymentMethodIcon = styled(View)`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${({ theme }) => theme.colors.background};
  justify-content: center;
  align-items: center;
  margin-right: 12px;
`;

export const PaymentMethodLabel = styled(Text)<{ selected: boolean }>`
  font-size: 16px;
  font-weight: ${({ selected }) => (selected ? "600" : "500")};
  color: ${({ theme, selected }) =>
    selected ? theme.colors.primary : theme.colors.text};
  margin-bottom: 2px;
`;

export const PaymentMethodDescription = styled(Text)`
  font-size: 14px;
  color: ${({ theme }) => theme.colors.text};
`;

export const PaymentMethodContent = styled(View)`
  margin-top: 16px;
  padding-top: 16px;
  border-top-width: 1px;
  border-top-color: ${({ theme }) => theme.colors.border};
`;

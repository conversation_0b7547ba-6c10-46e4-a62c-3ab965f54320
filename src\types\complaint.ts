export interface Complaint {
  id: string;
  status:
    | "pending"
    | "in_review"
    | "estimation_sent"
    | "approved"
    | "repair"
    | "in_progress"
    | "resolved"
    | "rejected"
    | "closed";
  description: string;
  serial_number: string;
  created_at: string;
  updated_at?: string;
  resolution?: string;
  resolved_at?: string;
  image?: string;
  warranty_status?: "in_warranty" | "out_of_warranty" | "partial_warranty";
  repair_cost?: number;
}

export interface ComplaintResponse {
  message: string;
  status: boolean;
  data: Complaint | Complaint[];
}

export interface CreateComplaintPayload {
  description: string;
  serial_number: string;
}

export interface UpdateComplaintPayload {
  id: string;
  status?: Complaint["status"];
  description?: string;
  resolution?: string;
}

export interface ResolveComplaintPayload {
  id: string;
  resolution: string;
}

export interface ComplaintState {
  complaints: Complaint[];
  complaintDetails: Complaint | null;
  loading: boolean;
  error: string | null;
}
